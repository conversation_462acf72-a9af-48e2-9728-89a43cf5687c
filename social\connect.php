<?php
/**
 * صفحة ربط الحسابات الاجتماعية من داخل النظام
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('../auth/login.php');
}

$current_user = $session->getCurrentUser();
$db = Database::getInstance();

$error_message = $_GET['error'] ?? '';
$success_message = $_GET['success'] ?? '';

// معالجة ربط حساب فيسبوك
if (isset($_GET['action']) && $_GET['action'] === 'facebook') {
    // التحقق من إعدادات فيسبوك
    if (empty(FB_APP_ID) || empty(FB_APP_SECRET)) {
        $error_message = 'إعدادات فيسبوك غير مكتملة. يرجى التواصل مع المطور.';
    } else {
        // إنشاء رابط تسجيل الدخول بفيسبوك
        $state = generate_random_string(32);
        $_SESSION['facebook_connect_state'] = $state;
        $_SESSION['connect_client_id'] = $_GET['client_id'] ?? 0;
        
        $facebook_login_url = 'https://www.facebook.com/v18.0/dialog/oauth?' . http_build_query([
            'client_id' => FB_APP_ID,
            'redirect_uri' => APP_URL . '/social/facebook-callback.php',
            'scope' => 'email,public_profile,pages_read_engagement,pages_manage_posts,pages_manage_metadata,pages_read_user_content,instagram_basic,instagram_manage_comments,instagram_manage_insights',
            'response_type' => 'code',
            'state' => $state
        ]);
        
        redirect($facebook_login_url);
    }
}

// جلب قائمة العملاء
$clients = $db->select(
    "SELECT id, name, company FROM clients WHERE status = 'active' ORDER BY name"
);

// جلب الحسابات المرتبطة بالفعل
$connected_accounts = $db->select(
    "SELECT sa.*, c.name as client_name 
     FROM social_accounts sa 
     JOIN clients c ON sa.client_id = c.id 
     WHERE sa.status = 'active' 
     ORDER BY c.name, sa.platform"
);

$page_title = 'ربط الحسابات الاجتماعية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .platform-card:hover { transform: translateY(-2px); transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="../index.php" class="text-gray-600 hover:text-gray-900 ml-4">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <h1 class="text-xl font-bold text-gray-900"><?php echo $page_title; ?></h1>
                </div>
                
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="text-sm text-gray-600">مرحباً، <?php echo htmlspecialchars($current_user['name']); ?></span>
                    <a href="../auth/logout.php" class="text-sm text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if ($error_message): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($success_message): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- Connect New Accounts -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">ربط حسابات جديدة</h2>
                    <p class="text-sm text-gray-600 mt-1">اختر المنصة والعميل لربط حساب جديد</p>
                </div>
                
                <div class="p-6">
                    <!-- Client Selection -->
                    <div class="mb-6">
                        <label for="client_select" class="block text-sm font-medium text-gray-700 mb-2">
                            اختر العميل
                        </label>
                        <select id="client_select" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">-- اختر العميل --</option>
                            <?php foreach ($clients as $client): ?>
                                <option value="<?php echo $client['id']; ?>">
                                    <?php echo htmlspecialchars($client['name']); ?>
                                    <?php if ($client['company']): ?>
                                        - <?php echo htmlspecialchars($client['company']); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- Social Platforms -->
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Facebook -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-blue-300" 
                             onclick="connectPlatform('facebook')">
                            <div class="mb-3">
                                <i class="fab fa-facebook-f text-3xl text-blue-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Facebook</h3>
                            <p class="text-xs text-gray-600 mt-1">ربط صفحات فيسبوك</p>
                        </div>

                        <!-- Instagram -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-pink-300" 
                             onclick="connectPlatform('instagram')">
                            <div class="mb-3">
                                <i class="fab fa-instagram text-3xl text-pink-600"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Instagram</h3>
                            <p class="text-xs text-gray-600 mt-1">ربط حسابات إنستجرام</p>
                        </div>

                        <!-- Twitter -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-blue-400 opacity-50" 
                             onclick="showComingSoon('Twitter')">
                            <div class="mb-3">
                                <i class="fab fa-twitter text-3xl text-blue-400"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Twitter</h3>
                            <p class="text-xs text-gray-600 mt-1">قريباً</p>
                        </div>

                        <!-- LinkedIn -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-blue-700 opacity-50" 
                             onclick="showComingSoon('LinkedIn')">
                            <div class="mb-3">
                                <i class="fab fa-linkedin text-3xl text-blue-700"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">LinkedIn</h3>
                            <p class="text-xs text-gray-600 mt-1">قريباً</p>
                        </div>

                        <!-- TikTok -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-black opacity-50" 
                             onclick="showComingSoon('TikTok')">
                            <div class="mb-3">
                                <i class="fab fa-tiktok text-3xl text-black"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">TikTok</h3>
                            <p class="text-xs text-gray-600 mt-1">قريباً</p>
                        </div>

                        <!-- Snapchat -->
                        <div class="platform-card border border-gray-200 rounded-lg p-4 text-center cursor-pointer hover:border-yellow-400 opacity-50" 
                             onclick="showComingSoon('Snapchat')">
                            <div class="mb-3">
                                <i class="fab fa-snapchat text-3xl text-yellow-400"></i>
                            </div>
                            <h3 class="font-medium text-gray-900">Snapchat</h3>
                            <p class="text-xs text-gray-600 mt-1">قريباً</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connected Accounts -->
            <div class="bg-white rounded-lg shadow-sm">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">الحسابات المرتبطة</h2>
                    <p class="text-sm text-gray-600 mt-1">الحسابات المرتبطة حالياً بالنظام</p>
                </div>
                
                <div class="p-6">
                    <?php if (empty($connected_accounts)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-link text-4xl text-gray-300 mb-4"></i>
                            <p class="text-gray-600">لا توجد حسابات مرتبطة حتى الآن</p>
                            <p class="text-sm text-gray-500 mt-2">ابدأ بربط حساب من المنصات المتاحة</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($connected_accounts as $account): ?>
                                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <div class="flex-shrink-0">
                                            <?php
                                            $platform_colors = [
                                                'facebook' => 'text-blue-600',
                                                'instagram' => 'text-pink-600',
                                                'twitter' => 'text-blue-400',
                                                'linkedin' => 'text-blue-700',
                                                'tiktok' => 'text-black',
                                                'snapchat' => 'text-yellow-400'
                                            ];
                                            $color = $platform_colors[$account['platform']] ?? 'text-gray-600';
                                            ?>
                                            <i class="fab fa-<?php echo $account['platform']; ?> text-xl <?php echo $color; ?>"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-900">
                                                <?php echo htmlspecialchars($account['account_name']); ?>
                                            </h4>
                                            <p class="text-sm text-gray-600">
                                                <?php echo htmlspecialchars($account['client_name']); ?>
                                                <?php if ($account['username']): ?>
                                                    • @<?php echo htmlspecialchars($account['username']); ?>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <span class="text-xs text-gray-500">
                                            <?php echo format_number($account['followers_count']); ?> متابع
                                        </span>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $account['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $account['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function connectPlatform(platform) {
            const clientId = document.getElementById('client_select').value;
            
            if (!clientId) {
                alert('يرجى اختيار العميل أولاً');
                return;
            }
            
            if (platform === 'facebook' || platform === 'instagram') {
                window.location.href = `connect.php?action=facebook&client_id=${clientId}`;
            } else {
                showComingSoon(platform);
            }
        }

        function showComingSoon(platform) {
            alert(`ربط ${platform} سيكون متاحاً قريباً!`);
        }

        // تحديث لون البطاقة عند اختيار العميل
        document.getElementById('client_select').addEventListener('change', function() {
            const cards = document.querySelectorAll('.platform-card');
            const hasClient = this.value !== '';
            
            cards.forEach(card => {
                if (hasClient && !card.classList.contains('opacity-50')) {
                    card.classList.add('hover:bg-gray-50');
                } else {
                    card.classList.remove('hover:bg-gray-50');
                }
            });
        });
    </script>
</body>
</html>
