<?php
/**
 * كلاس إدارة قاعدة البيانات
 * Social Media Management System
 */

require_once __DIR__ . '/../config/database.php';

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
        } catch (PDOException $e) {
            if (DEBUG_MODE) {
                die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            } else {
                die("خطأ في الاتصال بقاعدة البيانات");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $query, $params);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام SELECT لصف واحد
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $query, $params);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام INSERT
     */
    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $result = $stmt->execute($params);
            return $result ? $this->connection->lastInsertId() : false;
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $query, $params);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام UPDATE
     */
    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $query, $params);
            return false;
        }
    }
    
    /**
     * تنفيذ استعلام DELETE
     */
    public function delete($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            $stmt->execute($params);
            return $stmt->rowCount();
        } catch (PDOException $e) {
            $this->logError($e->getMessage(), $query, $params);
            return false;
        }
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * تسجيل الأخطاء
     */
    private function logError($error, $query = '', $params = []) {
        if (LOG_ERRORS) {
            $logMessage = date('Y-m-d H:i:s') . " - خطأ في قاعدة البيانات: " . $error . "\n";
            $logMessage .= "الاستعلام: " . $query . "\n";
            $logMessage .= "المعاملات: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n\n";
            
            if (!is_dir(ERROR_LOG_PATH)) {
                mkdir(ERROR_LOG_PATH, 0755, true);
            }
            
            file_put_contents(ERROR_LOG_PATH . 'database_errors.log', $logMessage, FILE_APPEND);
        }
    }
    
    /**
     * منع الاستنساخ
     */
    private function __clone() {}
    
    /**
     * منع إلغاء التسلسل
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}
