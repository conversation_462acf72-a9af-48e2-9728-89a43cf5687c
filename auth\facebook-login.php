<?php
/**
 * تسجيل الدخول بفيسبوك
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه للصفحة الرئيسية
if ($session->isLoggedIn()) {
    redirect('../index.php');
}

// التحقق من إعدادات فيسبوك
if (empty(FB_APP_ID) || empty(FB_APP_SECRET)) {
    show_error('إعدادات فيسبوك غير مكتملة. يرجى التواصل مع المطور.');
}

// إنشاء رابط تسجيل الدخول بفيسبوك
$facebook_login_url = 'https://www.facebook.com/v18.0/dialog/oauth?' . http_build_query([
    'client_id' => FB_APP_ID,
    'redirect_uri' => FB_REDIRECT_URI,
    'scope' => 'email,public_profile,pages_read_engagement,pages_manage_posts,pages_manage_metadata,pages_read_user_content,instagram_basic,instagram_manage_comments,instagram_manage_insights',
    'response_type' => 'code',
    'state' => $session->generateCSRFToken()
]);

// حفظ state في الجلسة للتحقق لاحقاً
$_SESSION['facebook_state'] = $session->generateCSRFToken();

// إعادة التوجيه لفيسبوك
redirect($facebook_login_url);
?>
