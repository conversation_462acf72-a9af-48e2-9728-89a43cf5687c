-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS social_media_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE social_media_manager;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    facebook_id VARCHAR(50) UNIQUE,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    profile_picture TEXT,
    role ENUM('admin', 'team', 'client') DEFAULT 'team',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_facebook_id (facebook_id),
    INDEX idx_email (email),
    INDEX idx_role (role)
);

-- جدول العملاء
CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    company VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    description TEXT,
    logo TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_status (status)
);

-- جدول الحسابات الاجتماعية
CREATE TABLE social_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    platform ENUM('facebook', 'instagram', 'tiktok', 'twitter', 'linkedin', 'snapchat') NOT NULL,
    account_id VARCHAR(100) NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    username VARCHAR(100),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP NULL,
    profile_picture TEXT,
    followers_count INT DEFAULT 0,
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    last_sync TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    UNIQUE KEY unique_account (platform, account_id),
    INDEX idx_client_platform (client_id, platform),
    INDEX idx_status (status)
);

-- جدول الرسائل
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    social_account_id INT NOT NULL,
    message_id VARCHAR(100) NOT NULL,
    sender_id VARCHAR(100) NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    message_type ENUM('text', 'image', 'video', 'audio', 'file') DEFAULT 'text',
    attachments JSON,
    status ENUM('unread', 'read', 'important', 'follow_up') DEFAULT 'unread',
    is_reply BOOLEAN DEFAULT FALSE,
    replied_at TIMESTAMP NULL,
    replied_by INT NULL,
    received_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_message (social_account_id, message_id),
    INDEX idx_status (status),
    INDEX idx_received_at (received_at),
    INDEX idx_sender (sender_id)
);

-- جدول المنشورات
CREATE TABLE posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    social_account_id INT NOT NULL,
    post_id VARCHAR(100) NOT NULL,
    content TEXT,
    media_urls JSON,
    post_type ENUM('text', 'image', 'video', 'carousel', 'story') DEFAULT 'text',
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    reach INT DEFAULT 0,
    impressions INT DEFAULT 0,
    published_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_post (social_account_id, post_id),
    INDEX idx_published_at (published_at),
    INDEX idx_engagement (likes_count, comments_count, shares_count)
);

-- جدول التعليقات
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT NOT NULL,
    comment_id VARCHAR(100) NOT NULL,
    parent_comment_id INT NULL,
    commenter_id VARCHAR(100) NOT NULL,
    commenter_name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    likes_count INT DEFAULT 0,
    status ENUM('pending', 'approved', 'hidden', 'replied') DEFAULT 'pending',
    replied_at TIMESTAMP NULL,
    replied_by INT NULL,
    reply_content TEXT NULL,
    commented_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_comment_id) REFERENCES comments(id) ON DELETE CASCADE,
    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_comment (post_id, comment_id),
    INDEX idx_status (status),
    INDEX idx_commented_at (commented_at),
    INDEX idx_commenter (commenter_id)
);

-- جدول المنشورات المجدولة
CREATE TABLE scheduled_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    social_account_id INT NOT NULL,
    content TEXT NOT NULL,
    media_files JSON,
    platforms JSON NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'published', 'failed', 'cancelled') DEFAULT 'pending',
    error_message TEXT NULL,
    created_by INT NOT NULL,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (social_account_id) REFERENCES social_accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_status (status)
);

-- جدول التقارير
CREATE TABLE reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    date_from DATE NOT NULL,
    date_to DATE NOT NULL,
    platforms JSON NOT NULL,
    data JSON NOT NULL,
    file_path VARCHAR(500),
    status ENUM('generating', 'completed', 'failed') DEFAULT 'generating',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_client_date (client_id, date_from, date_to),
    INDEX idx_status (status)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('message', 'comment', 'mention', 'report', 'system') NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    data JSON,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
);

-- جدول محاولات تسجيل الدخول
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_address VARCHAR(45) NOT NULL,
    email VARCHAR(100),
    attempts INT DEFAULT 1,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    locked_until TIMESTAMP NULL,
    INDEX idx_ip_address (ip_address),
    INDEX idx_email (email)
);

-- إدراج مستخدم إداري افتراضي
INSERT INTO users (name, email, role, status) VALUES 
('مدير النظام', '<EMAIL>', 'admin', 'active');

-- إدراج عميل تجريبي
INSERT INTO clients (name, company, email, description, created_by) VALUES 
('عميل تجريبي', 'شركة تجريبية', '<EMAIL>', 'عميل للاختبار', 1);
