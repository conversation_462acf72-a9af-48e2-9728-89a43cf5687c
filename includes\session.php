<?php
/**
 * إدارة الجلسات والأمان
 * Social Media Management System
 */

require_once __DIR__ . '/database.php';

class SessionManager {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->startSession();
    }
    
    /**
     * بدء الجلسة
     */
    private function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            session_set_cookie_params([
                'lifetime' => SESSION_LIFETIME,
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']),
                'httponly' => true,
                'samesite' => 'Strict'
            ]);
            
            session_start();
            
            // تجديد معرف الجلسة للأمان
            if (!isset($_SESSION['initiated'])) {
                session_regenerate_id(true);
                $_SESSION['initiated'] = true;
            }
            
            // التحقق من انتهاء صلاحية الجلسة
            if (isset($_SESSION['last_activity']) && 
                (time() - $_SESSION['last_activity'] > SESSION_LIFETIME)) {
                $this->destroySession();
                return;
            }
            
            $_SESSION['last_activity'] = time();
            
            // تحديث آخر نشاط في قاعدة البيانات
            if (isset($_SESSION['user_id'])) {
                $this->updateSessionActivity();
            }
        }
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function login($user_data) {
        // تجديد معرف الجلسة لمنع session fixation
        session_regenerate_id(true);
        
        $_SESSION['user_id'] = $user_data['id'];
        $_SESSION['user_name'] = $user_data['name'];
        $_SESSION['user_email'] = $user_data['email'];
        $_SESSION['user_role'] = $user_data['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
        
        // إنشاء CSRF token
        $_SESSION[CSRF_TOKEN_NAME] = $this->generateCSRFToken();
        
        // حفظ الجلسة في قاعدة البيانات
        $this->saveSessionToDatabase();
        
        // تحديث آخر تسجيل دخول
        $this->updateLastLogin($user_data['id']);
        
        return true;
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            // حذف الجلسة من قاعدة البيانات
            $this->deleteSessionFromDatabase();
        }
        
        $this->destroySession();
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'],
            'email' => $_SESSION['user_email'],
            'role' => $_SESSION['user_role']
        ];
    }
    
    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($required_role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $user_role = $_SESSION['user_role'];
        
        // ترتيب الصلاحيات من الأعلى للأقل
        $roles_hierarchy = [
            ROLE_ADMIN => 3,
            ROLE_TEAM => 2,
            ROLE_CLIENT => 1
        ];
        
        return isset($roles_hierarchy[$user_role]) && 
               isset($roles_hierarchy[$required_role]) &&
               $roles_hierarchy[$user_role] >= $roles_hierarchy[$required_role];
    }
    
    /**
     * توليد CSRF token
     */
    public function generateCSRFToken() {
        return bin2hex(random_bytes(32));
    }
    
    /**
     * التحقق من CSRF token
     */
    public function validateCSRFToken($token) {
        return isset($_SESSION[CSRF_TOKEN_NAME]) && 
               hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }
    
    /**
     * حفظ الجلسة في قاعدة البيانات
     */
    private function saveSessionToDatabase() {
        $session_id = session_id();
        $user_id = $_SESSION['user_id'];
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $expires_at = date('Y-m-d H:i:s', time() + SESSION_LIFETIME);
        
        // حذف الجلسات القديمة للمستخدم
        $this->db->delete(
            "DELETE FROM user_sessions WHERE user_id = ? OR expires_at < NOW()",
            [$user_id]
        );
        
        // إدراج الجلسة الجديدة
        $this->db->insert(
            "INSERT INTO user_sessions (id, user_id, ip_address, user_agent, expires_at) 
             VALUES (?, ?, ?, ?, ?)",
            [$session_id, $user_id, $ip_address, $user_agent, $expires_at]
        );
    }
    
    /**
     * تحديث نشاط الجلسة
     */
    private function updateSessionActivity() {
        $session_id = session_id();
        $this->db->update(
            "UPDATE user_sessions SET last_activity = NOW() WHERE id = ?",
            [$session_id]
        );
    }
    
    /**
     * حذف الجلسة من قاعدة البيانات
     */
    private function deleteSessionFromDatabase() {
        $session_id = session_id();
        $this->db->delete(
            "DELETE FROM user_sessions WHERE id = ?",
            [$session_id]
        );
    }
    
    /**
     * تحديث آخر تسجيل دخول
     */
    private function updateLastLogin($user_id) {
        $this->db->update(
            "UPDATE users SET last_login = NOW() WHERE id = ?",
            [$user_id]
        );
    }
    
    /**
     * تدمير الجلسة
     */
    private function destroySession() {
        $_SESSION = [];
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * تنظيف الجلسات المنتهية الصلاحية
     */
    public function cleanExpiredSessions() {
        $this->db->delete(
            "DELETE FROM user_sessions WHERE expires_at < NOW()"
        );
    }
    
    /**
     * التحقق من محاولات تسجيل الدخول
     */
    public function checkLoginAttempts($email, $ip_address) {
        // التحقق من محاولات IP
        $ip_attempts = $this->db->selectOne(
            "SELECT * FROM login_attempts WHERE ip_address = ? AND locked_until > NOW()",
            [$ip_address]
        );
        
        if ($ip_attempts) {
            return false;
        }
        
        // التحقق من محاولات البريد الإلكتروني
        if ($email) {
            $email_attempts = $this->db->selectOne(
                "SELECT * FROM login_attempts WHERE email = ? AND locked_until > NOW()",
                [$email]
            );
            
            if ($email_attempts) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * تسجيل محاولة تسجيل دخول فاشلة
     */
    public function recordFailedLogin($email, $ip_address) {
        // تسجيل محاولة IP
        $existing = $this->db->selectOne(
            "SELECT * FROM login_attempts WHERE ip_address = ?",
            [$ip_address]
        );
        
        if ($existing) {
            $attempts = $existing['attempts'] + 1;
            $locked_until = null;
            
            if ($attempts >= MAX_LOGIN_ATTEMPTS) {
                $locked_until = date('Y-m-d H:i:s', time() + LOGIN_LOCKOUT_TIME);
            }
            
            $this->db->update(
                "UPDATE login_attempts SET attempts = ?, last_attempt = NOW(), locked_until = ? WHERE ip_address = ?",
                [$attempts, $locked_until, $ip_address]
            );
        } else {
            $this->db->insert(
                "INSERT INTO login_attempts (ip_address, email, attempts) VALUES (?, ?, 1)",
                [$ip_address, $email]
            );
        }
    }
    
    /**
     * مسح محاولات تسجيل الدخول الناجحة
     */
    public function clearLoginAttempts($email, $ip_address) {
        $this->db->delete(
            "DELETE FROM login_attempts WHERE ip_address = ? OR email = ?",
            [$ip_address, $email]
        );
    }
}
