<?php
/**
 * الصفحة الرئيسية - نظام إدارة السوشيال ميديا
 * Social Media Management System
 */

// تضمين الملفات الأساسية
require_once 'config/database.php';
require_once 'includes/database.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';

// بدء إدارة الجلسة
$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('auth/login.php');
}

// الحصول على بيانات المستخدم الحالي
$current_user = $session->getCurrentUser();
$db = Database::getInstance();

// إحصائيات سريعة للوحة التحكم
$stats = [];

// عدد العملاء
$stats['clients'] = $db->selectOne("SELECT COUNT(*) as count FROM clients WHERE status = 'active'")['count'] ?? 0;

// عدد الحسابات الاجتماعية
$stats['social_accounts'] = $db->selectOne("SELECT COUNT(*) as count FROM social_accounts WHERE status = 'active'")['count'] ?? 0;

// عدد الرسائل غير المقروءة
$stats['unread_messages'] = $db->selectOne("SELECT COUNT(*) as count FROM messages WHERE status = 'unread'")['count'] ?? 0;

// عدد التعليقات المعلقة
$stats['pending_comments'] = $db->selectOne("SELECT COUNT(*) as count FROM comments WHERE status = 'pending'")['count'] ?? 0;

// الرسائل الأخيرة
$recent_messages = $db->select("
    SELECT m.*, sa.account_name, sa.platform, c.name as client_name
    FROM messages m
    JOIN social_accounts sa ON m.social_account_id = sa.id
    JOIN clients c ON sa.client_id = c.id
    ORDER BY m.received_at DESC
    LIMIT 5
");

// التعليقات الأخيرة
$recent_comments = $db->select("
    SELECT cm.*, p.content as post_content, sa.account_name, sa.platform, c.name as client_name
    FROM comments cm
    JOIN posts p ON cm.post_id = p.id
    JOIN social_accounts sa ON p.social_account_id = sa.id
    JOIN clients c ON sa.client_id = c.id
    WHERE cm.status = 'pending'
    ORDER BY cm.commented_at DESC
    LIMIT 5
");

// الإشعارات الأخيرة
$notifications = $db->select("
    SELECT * FROM notifications 
    WHERE user_id = ? AND is_read = 0
    ORDER BY created_at DESC
    LIMIT 5
", [$current_user['id']]);

$page_title = 'لوحة التحكم الرئيسية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900"><?php echo APP_NAME; ?></h1>
                </div>
                
                <!-- User Menu -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-600 hover:text-gray-900 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <?php if (count($notifications) > 0): ?>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    <?php echo count($notifications); ?>
                                </span>
                            <?php endif; ?>
                        </button>
                    </div>
                    
                    <!-- User Profile -->
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=<?php echo urlencode($current_user['name']); ?>&background=667eea&color=fff" alt="Profile">
                        <span class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($current_user['name']); ?></span>
                        <a href="auth/logout.php" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <nav class="w-64 bg-white shadow-sm h-screen sticky top-0">
            <div class="p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="index.php" class="flex items-center p-3 text-gray-700 bg-blue-50 rounded-lg">
                            <i class="fas fa-tachometer-alt ml-3"></i>
                            لوحة التحكم
                        </a>
                    </li>
                    <li>
                        <a href="clients/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-users ml-3"></i>
                            العملاء
                        </a>
                    </li>
                    <li>
                        <a href="messages/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-envelope ml-3"></i>
                            الرسائل
                            <?php if ($stats['unread_messages'] > 0): ?>
                                <span class="bg-red-500 text-white text-xs rounded-full px-2 py-1 mr-auto">
                                    <?php echo $stats['unread_messages']; ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li>
                        <a href="comments/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-comments ml-3"></i>
                            التعليقات
                            <?php if ($stats['pending_comments'] > 0): ?>
                                <span class="bg-yellow-500 text-white text-xs rounded-full px-2 py-1 mr-auto">
                                    <?php echo $stats['pending_comments']; ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li>
                        <a href="posts/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-edit ml-3"></i>
                            النشر والجدولة
                        </a>
                    </li>
                    <li>
                        <a href="reports/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-chart-bar ml-3"></i>
                            التقارير
                        </a>
                    </li>
                    <li>
                        <a href="social/connect.php" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-link ml-3"></i>
                            ربط الحسابات
                        </a>
                    </li>
                    <?php if ($session->hasPermission(ROLE_ADMIN)): ?>
                    <li>
                        <a href="admin/" class="flex items-center p-3 text-gray-700 hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-cog ml-3"></i>
                            إعدادات النظام
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">مرحباً، <?php echo htmlspecialchars($current_user['name']); ?></h2>
                <p class="text-gray-600">إليك نظرة سريعة على نشاط حساباتك اليوم</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i class="fas fa-users text-blue-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600">العملاء النشطين</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo format_number($stats['clients']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 rounded-lg">
                            <i class="fas fa-share-alt text-green-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600">الحسابات المرتبطة</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo format_number($stats['social_accounts']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i class="fas fa-envelope text-yellow-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600">رسائل جديدة</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo format_number($stats['unread_messages']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow-sm card-hover">
                    <div class="flex items-center">
                        <div class="p-3 bg-red-100 rounded-lg">
                            <i class="fas fa-comments text-red-600 text-xl"></i>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm text-gray-600">تعليقات معلقة</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo format_number($stats['pending_comments']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Messages -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold text-gray-900">الرسائل الأخيرة</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_messages)): ?>
                            <p class="text-gray-500 text-center py-4">لا توجد رسائل جديدة</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_messages as $message): ?>
                                    <div class="flex items-start space-x-3 space-x-reverse">
                                        <div class="flex-shrink-0">
                                            <i class="fab fa-<?php echo $message['platform']; ?> text-lg text-gray-600"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($message['sender_name']); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo truncate_text($message['content'], 60); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo time_ago($message['received_at']); ?> • <?php echo htmlspecialchars($message['client_name']); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="messages/" class="text-blue-600 hover:text-blue-800 text-sm font-medium">عرض جميع الرسائل</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Comments -->
                <div class="bg-white rounded-lg shadow-sm">
                    <div class="p-6 border-b">
                        <h3 class="text-lg font-semibold text-gray-900">التعليقات المعلقة</h3>
                    </div>
                    <div class="p-6">
                        <?php if (empty($recent_comments)): ?>
                            <p class="text-gray-500 text-center py-4">لا توجد تعليقات معلقة</p>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($recent_comments as $comment): ?>
                                    <div class="flex items-start space-x-3 space-x-reverse">
                                        <div class="flex-shrink-0">
                                            <i class="fab fa-<?php echo $comment['platform']; ?> text-lg text-gray-600"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($comment['commenter_name']); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo truncate_text($comment['content'], 60); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo time_ago($comment['commented_at']); ?> • <?php echo htmlspecialchars($comment['client_name']); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="mt-4 text-center">
                                <a href="comments/" class="text-blue-600 hover:text-blue-800 text-sm font-medium">عرض جميع التعليقات</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script>
        // تحديث الإشعارات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث الإشعارات
        }, <?php echo NOTIFICATION_CHECK_INTERVAL * 1000; ?>);
    </script>
</body>
</html>
