# Social Media Management System - Apache Configuration

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

# حماية ملفات الإعداد
<FilesMatch "^(config|includes)/">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# حماية ملفات قاعدة البيانات
<FilesMatch "\.(sql|db|sqlite)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# حماية ملفات السجلات
<FilesMatch "\.log$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# حماية ملفات النسخ الاحتياطية
<FilesMatch "\.(bak|backup|tmp)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# حماية ملف .htaccess نفسه
<Files ".htaccess">
    <RequireAll>
        Require all denied
    </RequireAll>
</Files>

# حماية ملفات Git
<FilesMatch "^\.git">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعادة توجيه الصفحات
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# إعادة توجيه للصفحة الرئيسية إذا لم يكن المستخدم مسجل دخول
RewriteRule ^$ index.php [L]

# حد أقصى لحجم الملفات المرفوعة
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_only_cookies 1

# منع عرض أخطاء PHP في الإنتاج
php_flag display_errors Off
php_flag log_errors On

# تحديد مجلد الأخطاء
php_value error_log logs/php_errors.log
