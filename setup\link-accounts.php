<?php
/**
 * صفحة ربط الحسابات الاجتماعية
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('../auth/login.php');
}

$current_user = $session->getCurrentUser();
$db = Database::getInstance();

// التحقق من وجود صفحات فيسبوك في الجلسة
$facebook_pages = $_SESSION['facebook_pages'] ?? [];
$facebook_access_token = $_SESSION['facebook_access_token'] ?? '';

// معالجة ربط الحسابات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['link_accounts'])) {
    verify_csrf_token();
    
    $selected_pages = $_POST['selected_pages'] ?? [];
    $client_id = (int)($_POST['client_id'] ?? 0);
    
    if (empty($selected_pages)) {
        $error_message = 'يرجى اختيار صفحة واحدة على الأقل';
    } elseif ($client_id <= 0) {
        $error_message = 'يرجى اختيار عميل صحيح';
    } else {
        $success_count = 0;
        
        foreach ($selected_pages as $page_id) {
            // البحث عن الصفحة في البيانات المحفوظة
            $page_data = null;
            foreach ($facebook_pages as $page) {
                if ($page['id'] === $page_id) {
                    $page_data = $page;
                    break;
                }
            }
            
            if ($page_data) {
                // التحقق من عدم وجود الحساب مسبقاً
                $existing = $db->selectOne(
                    "SELECT id FROM social_accounts WHERE platform = 'facebook' AND account_id = ?",
                    [$page_id]
                );
                
                if (!$existing) {
                    // إدراج الحساب الجديد
                    $account_id = $db->insert(
                        "INSERT INTO social_accounts 
                         (client_id, platform, account_id, account_name, access_token, profile_picture, followers_count, status, created_at) 
                         VALUES (?, 'facebook', ?, ?, ?, ?, ?, 'active', NOW())",
                        [
                            $client_id,
                            $page_data['id'],
                            $page_data['name'],
                            encrypt_data($page_data['access_token']),
                            $page_data['picture']['data']['url'] ?? '',
                            $page_data['fan_count'] ?? 0
                        ]
                    );
                    
                    if ($account_id) {
                        $success_count++;
                        
                        // محاولة ربط Instagram إذا كان متاحاً
                        try {
                            $instagram_url = "https://graph.facebook.com/v18.0/{$page_id}";
                            $instagram_params = [
                                'access_token' => $page_data['access_token'],
                                'fields' => 'instagram_business_account'
                            ];
                            
                            $instagram_response = file_get_contents($instagram_url . '?' . http_build_query($instagram_params));
                            $instagram_data = json_decode($instagram_response, true);
                            
                            if (isset($instagram_data['instagram_business_account']['id'])) {
                                $ig_account_id = $instagram_data['instagram_business_account']['id'];
                                
                                // الحصول على بيانات Instagram
                                $ig_info_url = "https://graph.facebook.com/v18.0/{$ig_account_id}";
                                $ig_info_params = [
                                    'access_token' => $page_data['access_token'],
                                    'fields' => 'id,username,name,profile_picture_url,followers_count'
                                ];
                                
                                $ig_info_response = file_get_contents($ig_info_url . '?' . http_build_query($ig_info_params));
                                $ig_info_data = json_decode($ig_info_response, true);
                                
                                if (isset($ig_info_data['id'])) {
                                    // التحقق من عدم وجود حساب Instagram مسبقاً
                                    $existing_ig = $db->selectOne(
                                        "SELECT id FROM social_accounts WHERE platform = 'instagram' AND account_id = ?",
                                        [$ig_account_id]
                                    );
                                    
                                    if (!$existing_ig) {
                                        $db->insert(
                                            "INSERT INTO social_accounts 
                                             (client_id, platform, account_id, account_name, username, access_token, profile_picture, followers_count, status, created_at) 
                                             VALUES (?, 'instagram', ?, ?, ?, ?, ?, ?, 'active', NOW())",
                                            [
                                                $client_id,
                                                $ig_info_data['id'],
                                                $ig_info_data['name'] ?? $ig_info_data['username'],
                                                $ig_info_data['username'],
                                                encrypt_data($page_data['access_token']),
                                                $ig_info_data['profile_picture_url'] ?? '',
                                                $ig_info_data['followers_count'] ?? 0
                                            ]
                                        );
                                    }
                                }
                            }
                        } catch (Exception $e) {
                            // تسجيل خطأ Instagram ولكن لا نوقف العملية
                            log_activity($current_user['id'], 'خطأ في ربط Instagram', $e->getMessage());
                        }
                    }
                }
            }
        }
        
        if ($success_count > 0) {
            $success_message = "تم ربط {$success_count} حساب بنجاح";
            
            // تسجيل النشاط
            log_activity($current_user['id'], 'ربط حسابات اجتماعية', "تم ربط {$success_count} حساب");
            
            // مسح البيانات من الجلسة
            unset($_SESSION['facebook_pages']);
            unset($_SESSION['facebook_access_token']);
            
            // إعادة التوجيه للصفحة الرئيسية بعد 3 ثوان
            header("refresh:3;url=../index.php");
        } else {
            $error_message = 'لم يتم ربط أي حساب. قد تكون الحسابات مربوطة مسبقاً.';
        }
    }
}

// جلب قائمة العملاء
$clients = $db->select(
    "SELECT id, name FROM clients WHERE status = 'active' ORDER BY name"
);

$page_title = 'ربط الحسابات الاجتماعية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    ربط الحسابات الاجتماعية
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    اختر الصفحات التي تريد ربطها بحساب العميل
                </p>
            </div>

            <?php if (isset($error_message)): ?>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (isset($success_message)): ?>
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                        <div class="mr-auto text-sm">
                            سيتم إعادة توجيهك للصفحة الرئيسية خلال 3 ثوان...
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($facebook_pages) && empty($success_message)): ?>
                <!-- Form -->
                <div class="bg-white shadow rounded-lg">
                    <form method="POST" action="" class="space-y-6 p-6">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Client Selection -->
                        <div>
                            <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                                اختر العميل
                            </label>
                            <select name="client_id" id="client_id" required 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">-- اختر العميل --</option>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?php echo $client['id']; ?>">
                                        <?php echo htmlspecialchars($client['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Facebook Pages -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                اختر الصفحات المراد ربطها
                            </label>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($facebook_pages as $page): ?>
                                    <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                                        <label class="flex items-start space-x-3 space-x-reverse cursor-pointer">
                                            <input type="checkbox" 
                                                   name="selected_pages[]" 
                                                   value="<?php echo htmlspecialchars($page['id']); ?>"
                                                   class="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-3 space-x-reverse mb-2">
                                                    <?php if (isset($page['picture']['data']['url'])): ?>
                                                        <img src="<?php echo htmlspecialchars($page['picture']['data']['url']); ?>" 
                                                             alt="صورة الصفحة" 
                                                             class="w-10 h-10 rounded-full">
                                                    <?php else: ?>
                                                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                                            <i class="fab fa-facebook-f text-blue-600"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h3 class="text-sm font-medium text-gray-900">
                                                            <?php echo htmlspecialchars($page['name']); ?>
                                                        </h3>
                                                        <p class="text-xs text-gray-500">
                                                            <?php echo htmlspecialchars($page['category'] ?? 'صفحة فيسبوك'); ?>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="text-xs text-gray-600">
                                                    <i class="fas fa-users ml-1"></i>
                                                    <?php echo format_number($page['fan_count'] ?? 0); ?> متابع
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-between items-center pt-6 border-t">
                            <a href="../index.php" 
                               class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                                تخطي هذه الخطوة
                            </a>
                            <button type="submit" 
                                    name="link_accounts"
                                    class="bg-blue-600 text-white py-2 px-6 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 font-medium">
                                <i class="fas fa-link ml-2"></i>
                                ربط الحسابات المختارة
                            </button>
                        </div>
                    </form>
                </div>
            <?php else: ?>
                <div class="bg-white shadow rounded-lg p-6 text-center">
                    <div class="mb-4">
                        <i class="fas fa-info-circle text-4xl text-blue-500"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        لا توجد صفحات متاحة للربط
                    </h3>
                    <p class="text-gray-600 mb-6">
                        لم يتم العثور على صفحات فيسبوك مرتبطة بحسابك أو تم ربطها مسبقاً
                    </p>
                    <a href="../index.php" 
                       class="bg-blue-600 text-white py-2 px-6 rounded-lg hover:bg-blue-700 transition duration-200 font-medium">
                        الذهاب للصفحة الرئيسية
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // تحديد/إلغاء تحديد جميع الصفحات
        function toggleAllPages() {
            const checkboxes = document.querySelectorAll('input[name="selected_pages[]"]');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        }

        // إضافة زر تحديد الكل
        document.addEventListener('DOMContentLoaded', function() {
            const pagesContainer = document.querySelector('.grid');
            if (pagesContainer) {
                const selectAllBtn = document.createElement('button');
                selectAllBtn.type = 'button';
                selectAllBtn.className = 'text-blue-600 hover:text-blue-800 text-sm font-medium mb-4';
                selectAllBtn.innerHTML = '<i class="fas fa-check-square ml-1"></i> تحديد/إلغاء تحديد الكل';
                selectAllBtn.onclick = toggleAllPages;
                
                pagesContainer.parentNode.insertBefore(selectAllBtn, pagesContainer);
            }
        });
    </script>
</body>
</html>
