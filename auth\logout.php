<?php
/**
 * تسجيل الخروج
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// تسجيل النشاط قبل تسجيل الخروج
if ($session->isLoggedIn()) {
    $current_user = $session->getCurrentUser();
    log_activity($current_user['id'], 'تسجيل خروج', 'تسجيل خروج من النظام');
}

// تسجيل الخروج
$session->logout();

// إعادة التوجيه لصفحة تسجيل الدخول
redirect('login.php');
?>
