<?php
/**
 * معالجة callback من فيسبوك لربط الحسابات
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('../auth/login.php');
}

$current_user = $session->getCurrentUser();
$db = Database::getInstance();

// التحقق من وجود كود التفويض
if (!isset($_GET['code'])) {
    $error = $_GET['error_description'] ?? 'فشل في الاتصال بفيسبوك';
    redirect('connect.php?error=' . urlencode($error));
}

// التحقق من state للحماية من CSRF
if (!isset($_GET['state']) || $_GET['state'] !== ($_SESSION['facebook_connect_state'] ?? '')) {
    redirect('connect.php?error=' . urlencode('خطأ في التحقق من الأمان'));
}

$code = $_GET['code'];
$client_id = (int)($_SESSION['connect_client_id'] ?? 0);

if ($client_id <= 0) {
    redirect('connect.php?error=' . urlencode('معرف العميل غير صحيح'));
}

try {
    // الحصول على access token
    $token_url = 'https://graph.facebook.com/v18.0/oauth/access_token';
    $token_params = [
        'client_id' => FB_APP_ID,
        'client_secret' => FB_APP_SECRET,
        'redirect_uri' => APP_URL . '/social/facebook-callback.php',
        'code' => $code
    ];
    
    $token_response = file_get_contents($token_url . '?' . http_build_query($token_params));
    $token_data = json_decode($token_response, true);
    
    if (!isset($token_data['access_token'])) {
        throw new Exception('فشل في الحصول على رمز الوصول من فيسبوك');
    }
    
    $access_token = $token_data['access_token'];
    
    // جلب صفحات فيسبوك المرتبطة بالمستخدم
    $pages_url = 'https://graph.facebook.com/v18.0/me/accounts';
    $pages_params = [
        'access_token' => $access_token,
        'fields' => 'id,name,access_token,category,picture,fan_count,instagram_business_account'
    ];
    
    $pages_response = file_get_contents($pages_url . '?' . http_build_query($pages_params));
    $pages_data = json_decode($pages_response, true);
    
    if (!isset($pages_data['data']) || empty($pages_data['data'])) {
        throw new Exception('لم يتم العثور على صفحات فيسبوك مرتبطة بحسابك');
    }
    
    $success_count = 0;
    $instagram_count = 0;
    
    foreach ($pages_data['data'] as $page) {
        // ربط صفحة فيسبوك
        $existing_fb = $db->selectOne(
            "SELECT id FROM social_accounts WHERE platform = 'facebook' AND account_id = ?",
            [$page['id']]
        );
        
        if (!$existing_fb) {
            $fb_account_id = $db->insert(
                "INSERT INTO social_accounts 
                 (client_id, platform, account_id, account_name, access_token, profile_picture, followers_count, status, created_at) 
                 VALUES (?, 'facebook', ?, ?, ?, ?, ?, 'active', NOW())",
                [
                    $client_id,
                    $page['id'],
                    $page['name'],
                    encrypt_data($page['access_token']),
                    $page['picture']['data']['url'] ?? '',
                    $page['fan_count'] ?? 0
                ]
            );
            
            if ($fb_account_id) {
                $success_count++;
            }
        }
        
        // محاولة ربط Instagram إذا كان متاحاً
        if (isset($page['instagram_business_account']['id'])) {
            $ig_account_id = $page['instagram_business_account']['id'];
            
            try {
                // الحصول على بيانات Instagram
                $ig_info_url = "https://graph.facebook.com/v18.0/{$ig_account_id}";
                $ig_info_params = [
                    'access_token' => $page['access_token'],
                    'fields' => 'id,username,name,profile_picture_url,followers_count'
                ];
                
                $ig_info_response = file_get_contents($ig_info_url . '?' . http_build_query($ig_info_params));
                $ig_info_data = json_decode($ig_info_response, true);
                
                if (isset($ig_info_data['id'])) {
                    // التحقق من عدم وجود حساب Instagram مسبقاً
                    $existing_ig = $db->selectOne(
                        "SELECT id FROM social_accounts WHERE platform = 'instagram' AND account_id = ?",
                        [$ig_account_id]
                    );
                    
                    if (!$existing_ig) {
                        $ig_db_id = $db->insert(
                            "INSERT INTO social_accounts 
                             (client_id, platform, account_id, account_name, username, access_token, profile_picture, followers_count, status, created_at) 
                             VALUES (?, 'instagram', ?, ?, ?, ?, ?, ?, 'active', NOW())",
                            [
                                $client_id,
                                $ig_info_data['id'],
                                $ig_info_data['name'] ?? $ig_info_data['username'],
                                $ig_info_data['username'],
                                encrypt_data($page['access_token']),
                                $ig_info_data['profile_picture_url'] ?? '',
                                $ig_info_data['followers_count'] ?? 0
                            ]
                        );
                        
                        if ($ig_db_id) {
                            $instagram_count++;
                        }
                    }
                }
            } catch (Exception $e) {
                // تسجيل خطأ Instagram ولكن لا نوقف العملية
                log_activity($current_user['id'], 'خطأ في ربط Instagram', $e->getMessage());
            }
        }
    }
    
    // تسجيل النشاط
    $activity_message = "تم ربط {$success_count} صفحة فيسبوك";
    if ($instagram_count > 0) {
        $activity_message .= " و {$instagram_count} حساب إنستجرام";
    }
    
    log_activity($current_user['id'], 'ربط حسابات اجتماعية', $activity_message);
    
    // مسح البيانات من الجلسة
    unset($_SESSION['facebook_connect_state']);
    unset($_SESSION['connect_client_id']);
    
    // إعادة التوجيه مع رسالة نجاح
    $success_message = "تم ربط {$success_count} صفحة فيسبوك بنجاح";
    if ($instagram_count > 0) {
        $success_message .= " مع {$instagram_count} حساب إنستجرام";
    }
    
    redirect('connect.php?success=' . urlencode($success_message));
    
} catch (Exception $e) {
    // تسجيل الخطأ
    if (LOG_ERRORS) {
        $error_message = date('Y-m-d H:i:s') . " - خطأ في ربط حسابات فيسبوك: " . $e->getMessage() . "\n";
        file_put_contents(ERROR_LOG_PATH . 'facebook_connect.log', $error_message, FILE_APPEND);
    }
    
    // مسح البيانات من الجلسة
    unset($_SESSION['facebook_connect_state']);
    unset($_SESSION['connect_client_id']);
    
    redirect('connect.php?error=' . urlencode('فشل في ربط الحسابات: ' . $e->getMessage()));
}
?>
