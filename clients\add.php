<?php
/**
 * صفحة إضافة عميل جديد
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('../auth/login.php');
}

// التحقق من الصلاحيات
if (!$session->hasPermission(ROLE_TEAM)) {
    show_error('ليس لديك صلاحية لإضافة العملاء', 403);
}

$current_user = $session->getCurrentUser();
$db = Database::getInstance();

$error_message = '';
$success_message = '';

// معالجة إضافة العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verify_csrf_token();
    
    $name = sanitize_input($_POST['name'] ?? '');
    $company = sanitize_input($_POST['company'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $description = sanitize_input($_POST['description'] ?? '');
    
    // التحقق من البيانات المطلوبة
    if (empty($name)) {
        $error_message = 'اسم العميل مطلوب';
    } elseif (!empty($email) && !validate_email($email)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } elseif (!empty($phone) && !validate_phone($phone)) {
        $error_message = 'رقم الهاتف غير صحيح';
    } else {
        // التحقق من عدم وجود عميل بنفس الاسم
        $existing_client = $db->selectOne(
            "SELECT id FROM clients WHERE name = ?",
            [$name]
        );
        
        if ($existing_client) {
            $error_message = 'يوجد عميل بنفس الاسم بالفعل';
        } else {
            // معالجة رفع الشعار
            $logo_path = '';
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
                $upload_result = upload_file($_FILES['logo'], 'clients');
                if ($upload_result['success']) {
                    $logo_path = $upload_result['path'];
                } else {
                    $error_message = $upload_result['message'];
                }
            }
            
            if (empty($error_message)) {
                // إدراج العميل الجديد
                $client_id = $db->insert(
                    "INSERT INTO clients (name, company, email, phone, description, logo, created_by, created_at) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, NOW())",
                    [$name, $company, $email, $phone, $description, $logo_path, $current_user['id']]
                );
                
                if ($client_id) {
                    $success_message = 'تم إضافة العميل بنجاح';
                    
                    // تسجيل النشاط
                    log_activity($current_user['id'], 'إضافة عميل جديد', "تم إضافة العميل: {$name}");
                    
                    // إعادة التوجيه لصفحة العملاء بعد 2 ثانية
                    header("refresh:2;url=index.php");
                } else {
                    $error_message = 'فشل في إضافة العميل';
                }
            }
        }
    }
}

$page_title = 'إضافة عميل جديد';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
    </style>
</head>
<body class="bg-gray-50">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="text-gray-600 hover:text-gray-900 ml-4">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <h1 class="text-xl font-bold text-gray-900"><?php echo $page_title; ?></h1>
                </div>
                
                <div class="flex items-center space-x-2 space-x-reverse">
                    <span class="text-sm text-gray-600">مرحباً، <?php echo htmlspecialchars($current_user['name']); ?></span>
                    <a href="../auth/logout.php" class="text-sm text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if ($error_message): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($success_message): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
                <div class="mt-2 text-sm">
                    سيتم إعادة توجيهك لصفحة العملاء خلال ثانيتين...
                </div>
            </div>
        <?php endif; ?>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">بيانات العميل الجديد</h2>
                <p class="text-sm text-gray-600 mt-1">أدخل بيانات العميل لإضافته للنظام</p>
            </div>
            
            <form method="POST" action="" enctype="multipart/form-data" class="p-6 space-y-6">
                <?php echo csrf_field(); ?>
                
                <!-- Client Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم العميل <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                           required 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="أدخل اسم العميل">
                </div>

                <!-- Company -->
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم الشركة
                    </label>
                    <input type="text" 
                           id="company" 
                           name="company" 
                           value="<?php echo htmlspecialchars($_POST['company'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="أدخل اسم الشركة (اختياري)">
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="أدخل البريد الإلكتروني (اختياري)">
                </div>

                <!-- Phone -->
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                    </label>
                    <input type="tel" 
                           id="phone" 
                           name="phone" 
                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="أدخل رقم الهاتف (اختياري)">
                </div>

                <!-- Logo -->
                <div>
                    <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">
                        شعار العميل
                    </label>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <input type="file" 
                               id="logo" 
                               name="logo" 
                               accept="image/*"
                               class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div class="text-sm text-gray-500">
                            JPG, PNG, GIF حتى 10MB
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        وصف العميل
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="أدخل وصف مختصر عن العميل ونشاطه (اختياري)"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                </div>

                <!-- Submit Buttons -->
                <div class="flex justify-between items-center pt-6 border-t">
                    <a href="index.php" 
                       class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                        <i class="fas fa-arrow-right ml-1"></i>
                        العودة للقائمة
                    </a>
                    <div class="flex space-x-3 space-x-reverse">
                        <button type="reset" 
                                class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50">
                            مسح البيانات
                        </button>
                        <button type="submit" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200">
                            <i class="fas fa-save ml-2"></i>
                            حفظ العميل
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // معاينة الشعار قبل الرفع
        document.getElementById('logo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // يمكن إضافة معاينة للصورة هنا
                };
                reader.readAsDataURL(file);
            }
        });

        // Auto-focus on name field
        document.getElementById('name').focus();
    </script>
</body>
</html>
