<?php
/**
 * صفحة تسجيل مستخدم جديد
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه للصفحة الرئيسية
if ($session->isLoggedIn()) {
    redirect('../index.php');
}

$error_message = '';
$success_message = '';

// معالجة تسجيل المستخدم الجديد
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // التحقق من البيانات المطلوبة
    if (empty($name) || empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال جميع البيانات المطلوبة';
    } 
    // التحقق من صحة البريد الإلكتروني
    elseif (!validate_email($email)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    }
    // التحقق من طول كلمة المرور
    elseif (strlen($password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    // التحقق من تطابق كلمة المرور
    elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    }
    else {
        $db = Database::getInstance();
        
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        $existing_user = $db->selectOne(
            "SELECT id FROM users WHERE email = ?",
            [$email]
        );
        
        if ($existing_user) {
            $error_message = 'البريد الإلكتروني مستخدم بالفعل';
        } else {
            // تشفير كلمة المرور
            $hashed_password = hash_password($password);
            
            // إنشاء المستخدم الجديد
            $user_id = $db->insert(
                "INSERT INTO users (name, email, password, role, status, created_at) 
                 VALUES (?, ?, ?, 'team', 'active', NOW())",
                [$name, $email, $hashed_password]
            );
            
            if ($user_id) {
                $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                
                // تسجيل النشاط
                log_activity($user_id, 'إنشاء حساب جديد', 'تم إنشاء حساب جديد للمستخدم: ' . $name);
                
                // إعادة التوجيه لصفحة تسجيل الدخول بعد 3 ثوان
                header("refresh:3;url=login.php");
            } else {
                $error_message = 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى';
            }
        }
    }
}

$page_title = 'إنشاء حساب جديد';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center">
    
    <div class="max-w-md w-full mx-4">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="bg-white p-4 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <i class="fas fa-share-alt text-3xl text-blue-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2"><?php echo APP_NAME; ?></h1>
            <p class="text-blue-100">إنشاء حساب جديد</p>
        </div>

        <!-- Register Form -->
        <div class="bg-white rounded-lg shadow-xl p-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-6 text-center">إنشاء حساب جديد</h2>
            
            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle ml-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                    <div class="mt-2 text-sm">
                        سيتم إعادة توجيهك لصفحة تسجيل الدخول خلال 3 ثوان...
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!$success_message): ?>
            <form method="POST" action="" class="space-y-6">
                <?php echo csrf_field(); ?>
                
                <!-- Name Field -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        الاسم الكامل
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                               required 
                               class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل اسمك الكامل">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني
                    </label>
                    <div class="relative">
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               required 
                               class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل بريدك الإلكتروني">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               required 
                               minlength="6"
                               class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أدخل كلمة المرور (6 أحرف على الأقل)">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <button type="button" 
                                onclick="togglePassword('password')" 
                                class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i id="password-toggle" class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                        تأكيد كلمة المرور
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="confirm_password" 
                               name="confirm_password" 
                               required 
                               minlength="6"
                               class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="أعد إدخال كلمة المرور">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <button type="button" 
                                onclick="togglePassword('confirm_password')" 
                                class="absolute inset-y-0 left-0 pl-3 flex items-center">
                            <i id="confirm_password-toggle" class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- Submit Button -->
                <button type="submit" 
                        class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 font-medium">
                    <i class="fas fa-user-plus ml-2"></i>
                    إنشاء الحساب
                </button>
            </form>
            <?php endif; ?>

            <!-- Footer -->
            <div class="mt-8 text-center text-sm text-gray-600">
                <p>لديك حساب بالفعل؟ 
                    <a href="login.php" class="text-blue-600 hover:text-blue-800 font-medium">
                        تسجيل الدخول
                    </a>
                </p>
            </div>
        </div>

        <!-- Footer Info -->
        <div class="mt-8 text-center text-blue-100 text-sm">
            <p>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </div>

    <script>
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        // Auto-focus on name field
        document.getElementById('name').focus();
    </script>
</body>
</html>
