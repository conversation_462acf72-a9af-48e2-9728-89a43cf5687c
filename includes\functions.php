<?php
/**
 * الوظائف المساعدة العامة
 * Social Media Management System
 */

/**
 * تنظيف وتأمين البيانات المدخلة
 */
function sanitize_input($data) {
    if (is_array($data)) {
        return array_map('sanitize_input', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * التحقق من صحة رقم الهاتف
 */
function validate_phone($phone) {
    return preg_match('/^[\+]?[0-9\s\-\(\)]{10,20}$/', $phone);
}

/**
 * تشفير كلمة المرور
 */
function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * التحقق من كلمة المرور
 */
function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * توليد رمز عشوائي
 */
function generate_random_string($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function format_arabic_date($date, $format = 'Y-m-d H:i') {
    $arabic_months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $formatted = date($format, $timestamp);
    
    // استبدال أسماء الشهور
    foreach ($arabic_months as $num => $name) {
        $formatted = str_replace(date('F', mktime(0, 0, 0, $num, 1)), $name, $formatted);
    }
    
    return $formatted;
}

/**
 * تحويل الوقت إلى "منذ" (مثل: منذ 5 دقائق)
 */
function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'الآن';
    if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
    if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
    if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
    if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
    
    return 'منذ ' . floor($time/31536000) . ' سنة';
}

/**
 * تنسيق الأرقام (مثل: 1.2K, 1.5M)
 */
function format_number($number) {
    if ($number >= 1000000) {
        return round($number / 1000000, 1) . 'M';
    } elseif ($number >= 1000) {
        return round($number / 1000, 1) . 'K';
    }
    return number_format($number);
}

/**
 * تحويل حجم الملف إلى تنسيق قابل للقراءة
 */
function format_file_size($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * التحقق من نوع الملف المسموح
 */
function is_allowed_file_type($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_FILE_TYPES);
}

/**
 * رفع الملفات بأمان
 */
function upload_file($file, $destination_folder = 'uploads') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    // التحقق من الأخطاء
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    // التحقق من حجم الملف
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    // التحقق من نوع الملف
    if (!is_allowed_file_type($file['name'])) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    // إنشاء اسم ملف فريد
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    
    // إنشاء المجلد إذا لم يكن موجوداً
    $upload_path = UPLOAD_PATH . $destination_folder . '/';
    if (!is_dir($upload_path)) {
        mkdir($upload_path, 0755, true);
    }
    
    $full_path = $upload_path . $filename;
    
    // نقل الملف
    if (move_uploaded_file($file['tmp_name'], $full_path)) {
        return [
            'success' => true,
            'filename' => $filename,
            'path' => $destination_folder . '/' . $filename,
            'size' => $file['size']
        ];
    }
    
    return ['success' => false, 'message' => 'فشل في حفظ الملف'];
}

/**
 * حذف الملف
 */
function delete_file($file_path) {
    $full_path = UPLOAD_PATH . $file_path;
    if (file_exists($full_path)) {
        return unlink($full_path);
    }
    return false;
}

/**
 * إرسال استجابة JSON
 */
function send_json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * إعادة التوجيه
 */
function redirect($url, $permanent = false) {
    $status_code = $permanent ? 301 : 302;
    http_response_code($status_code);
    header('Location: ' . $url);
    exit;
}

/**
 * عرض رسالة خطأ وإيقاف التنفيذ
 */
function show_error($message, $code = 500) {
    http_response_code($code);
    include __DIR__ . '/../views/error.php';
    exit;
}

/**
 * تسجيل الأحداث
 */
function log_activity($user_id, $action, $details = '') {
    $db = Database::getInstance();
    
    $log_data = [
        'user_id' => $user_id,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    // يمكن إضافة جدول activity_logs لاحقاً
    if (LOG_ERRORS) {
        $log_message = date('Y-m-d H:i:s') . " - المستخدم {$user_id}: {$action} - {$details}\n";
        file_put_contents(ERROR_LOG_PATH . 'activity.log', $log_message, FILE_APPEND);
    }
}

/**
 * التحقق من معدل الطلبات (Rate Limiting)
 */
function check_rate_limit($identifier, $limit = API_RATE_LIMIT, $window = API_RATE_WINDOW) {
    $cache_key = 'rate_limit_' . md5($identifier);
    $cache_file = __DIR__ . '/../temp/' . $cache_key;
    
    if (!is_dir(dirname($cache_file))) {
        mkdir(dirname($cache_file), 0755, true);
    }
    
    $current_time = time();
    $requests = [];
    
    // قراءة الطلبات السابقة
    if (file_exists($cache_file)) {
        $data = file_get_contents($cache_file);
        $requests = json_decode($data, true) ?: [];
    }
    
    // تنظيف الطلبات القديمة
    $requests = array_filter($requests, function($timestamp) use ($current_time, $window) {
        return ($current_time - $timestamp) < $window;
    });
    
    // التحقق من الحد الأقصى
    if (count($requests) >= $limit) {
        return false;
    }
    
    // إضافة الطلب الحالي
    $requests[] = $current_time;
    
    // حفظ البيانات
    file_put_contents($cache_file, json_encode($requests));
    
    return true;
}

/**
 * تشفير البيانات الحساسة
 */
function encrypt_data($data) {
    $key = ENCRYPTION_KEY;
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return base64_encode($iv . $encrypted);
}

/**
 * فك تشفير البيانات
 */
function decrypt_data($encrypted_data) {
    $key = ENCRYPTION_KEY;
    $data = base64_decode($encrypted_data);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

/**
 * التحقق من صحة رمز CSRF
 */
function verify_csrf_token() {
    $session = new SessionManager();
    $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
    
    if (!$session->validateCSRFToken($token)) {
        send_json_response(['error' => 'رمز الأمان غير صحيح'], 403);
    }
}

/**
 * إنشاء حقل CSRF مخفي
 */
function csrf_field() {
    $session = new SessionManager();
    $token = $_SESSION[CSRF_TOKEN_NAME] ?? $session->generateCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

/**
 * تنظيف النص من HTML
 */
function strip_html_tags($text, $allowed_tags = '') {
    return strip_tags($text, $allowed_tags);
}

/**
 * اقتطاع النص مع الحفاظ على الكلمات
 */
function truncate_text($text, $length = 100, $suffix = '...') {
    if (mb_strlen($text) <= $length) {
        return $text;
    }
    
    $truncated = mb_substr($text, 0, $length);
    $last_space = mb_strrpos($truncated, ' ');
    
    if ($last_space !== false) {
        $truncated = mb_substr($truncated, 0, $last_space);
    }
    
    return $truncated . $suffix;
}
