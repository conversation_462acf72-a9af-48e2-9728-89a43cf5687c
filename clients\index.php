<?php
/**
 * صفحة إدارة العملاء
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();

// التحقق من تسجيل الدخول
if (!$session->isLoggedIn()) {
    redirect('../auth/login.php');
}

$current_user = $session->getCurrentUser();
$db = Database::getInstance();

// معالجة حذف العميل
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_client'])) {
    verify_csrf_token();
    
    $client_id = (int)$_POST['client_id'];
    
    if ($session->hasPermission(ROLE_ADMIN)) {
        $deleted = $db->delete("DELETE FROM clients WHERE id = ?", [$client_id]);
        
        if ($deleted) {
            $success_message = 'تم حذف العميل بنجاح';
            log_activity($current_user['id'], 'حذف عميل', "تم حذف العميل رقم {$client_id}");
        } else {
            $error_message = 'فشل في حذف العميل';
        }
    } else {
        $error_message = 'ليس لديك صلاحية لحذف العملاء';
    }
}

// جلب قائمة العملاء مع إحصائيات
$clients_query = "
    SELECT c.*, 
           COUNT(DISTINCT sa.id) as social_accounts_count,
           COUNT(DISTINCT CASE WHEN m.status = 'unread' THEN m.id END) as unread_messages,
           COUNT(DISTINCT CASE WHEN cm.status = 'pending' THEN cm.id END) as pending_comments,
           u.name as created_by_name
    FROM clients c
    LEFT JOIN social_accounts sa ON c.id = sa.client_id AND sa.status = 'active'
    LEFT JOIN messages m ON sa.id = m.social_account_id
    LEFT JOIN posts p ON sa.id = p.social_account_id
    LEFT JOIN comments cm ON p.id = cm.post_id
    LEFT JOIN users u ON c.created_by = u.id
    GROUP BY c.id
    ORDER BY c.created_at DESC
";

$clients = $db->select($clients_query);

$page_title = 'إدارة العملاء';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
    </style>
</head>
<body class="bg-gray-50">
    
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="../index.php" class="text-gray-600 hover:text-gray-900 ml-4">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <h1 class="text-xl font-bold text-gray-900"><?php echo $page_title; ?></h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="add.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة عميل جديد
                    </a>
                    
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <span class="text-sm text-gray-600">مرحباً، <?php echo htmlspecialchars($current_user['name']); ?></span>
                        <a href="../auth/logout.php" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <?php if (isset($error_message)): ?>
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (isset($success_message)): ?>
            <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-6">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-blue-100 rounded-lg">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm text-gray-600">إجمالي العملاء</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($clients); ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-green-100 rounded-lg">
                        <i class="fas fa-share-alt text-green-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm text-gray-600">الحسابات المرتبطة</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo array_sum(array_column($clients, 'social_accounts_count')); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-yellow-100 rounded-lg">
                        <i class="fas fa-envelope text-yellow-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm text-gray-600">رسائل غير مقروءة</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo array_sum(array_column($clients, 'unread_messages')); ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex items-center">
                    <div class="p-3 bg-red-100 rounded-lg">
                        <i class="fas fa-comments text-red-600 text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm text-gray-600">تعليقات معلقة</p>
                        <p class="text-2xl font-bold text-gray-900">
                            <?php echo array_sum(array_column($clients, 'pending_comments')); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Clients List -->
        <?php if (empty($clients)): ?>
            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                <div class="mb-4">
                    <i class="fas fa-users text-6xl text-gray-300"></i>
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">لا يوجد عملاء حتى الآن</h3>
                <p class="text-gray-600 mb-6">ابدأ بإضافة عميل جديد لإدارة حساباته الاجتماعية</p>
                <a href="add.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition duration-200">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة عميل جديد
                </a>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($clients as $client): ?>
                    <div class="bg-white rounded-lg shadow-sm card-hover">
                        <!-- Client Header -->
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3 space-x-reverse">
                                    <?php if ($client['logo']): ?>
                                        <img src="../uploads/<?php echo htmlspecialchars($client['logo']); ?>" 
                                             alt="شعار العميل" 
                                             class="w-12 h-12 rounded-full object-cover">
                                    <?php else: ?>
                                        <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-building text-gray-500 text-lg"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <?php echo htmlspecialchars($client['name']); ?>
                                        </h3>
                                        <?php if ($client['company']): ?>
                                            <p class="text-sm text-gray-600">
                                                <?php echo htmlspecialchars($client['company']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $client['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                        <?php echo $client['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <?php if ($client['description']): ?>
                                <p class="text-sm text-gray-600">
                                    <?php echo truncate_text($client['description'], 100); ?>
                                </p>
                            <?php endif; ?>
                        </div>

                        <!-- Client Stats -->
                        <div class="p-6 border-b">
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div>
                                    <p class="text-2xl font-bold text-blue-600"><?php echo $client['social_accounts_count']; ?></p>
                                    <p class="text-xs text-gray-600">حساب مرتبط</p>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-yellow-600"><?php echo $client['unread_messages']; ?></p>
                                    <p class="text-xs text-gray-600">رسالة جديدة</p>
                                </div>
                                <div>
                                    <p class="text-2xl font-bold text-red-600"><?php echo $client['pending_comments']; ?></p>
                                    <p class="text-xs text-gray-600">تعليق معلق</p>
                                </div>
                            </div>
                        </div>

                        <!-- Client Actions -->
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="view.php?id=<?php echo $client['id']; ?>" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        <i class="fas fa-eye ml-1"></i>
                                        عرض
                                    </a>
                                    <a href="edit.php?id=<?php echo $client['id']; ?>" 
                                       class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        <i class="fas fa-edit ml-1"></i>
                                        تعديل
                                    </a>
                                    <?php if ($session->hasPermission(ROLE_ADMIN)): ?>
                                        <button onclick="deleteClient(<?php echo $client['id']; ?>, '<?php echo htmlspecialchars($client['name']); ?>')" 
                                                class="text-red-600 hover:text-red-800 text-sm font-medium">
                                            <i class="fas fa-trash ml-1"></i>
                                            حذف
                                        </button>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="text-xs text-gray-500">
                                    <?php echo format_arabic_date($client['created_at'], 'd/m/Y'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">تأكيد الحذف</h3>
            <p class="text-gray-600 mb-6">هل أنت متأكد من حذف العميل "<span id="clientName"></span>"؟ سيتم حذف جميع البيانات المرتبطة به.</p>
            
            <form method="POST" action="">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="client_id" id="deleteClientId">
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button type="button" onclick="closeDeleteModal()" 
                            class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        إلغاء
                    </button>
                    <button type="submit" name="delete_client" 
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                        حذف
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function deleteClient(clientId, clientName) {
            document.getElementById('deleteClientId').value = clientId;
            document.getElementById('clientName').textContent = clientName;
            document.getElementById('deleteModal').classList.remove('hidden');
            document.getElementById('deleteModal').classList.add('flex');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
            document.getElementById('deleteModal').classList.remove('flex');
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
    </script>
</body>
</html>
