<?php
/**
 * إعدادات قاعدة البيانات
 * Social Media Management System
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'social_media_manager');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات Facebook API
define('FB_APP_ID', '');
define('FB_APP_SECRET', '');
define('FB_REDIRECT_URI', 'http://localhost/media/auth/facebook-callback.php');

// إعدادات Instagram API
define('IG_CLIENT_ID', '');
define('IG_CLIENT_SECRET', '');
define('IG_REDIRECT_URI', 'http://localhost/media/auth/instagram-callback.php');

// إعدادات التطبيق العامة
define('APP_NAME', 'Social Media Manager');
define('APP_URL', 'http://localhost/media');
define('UPLOAD_PATH', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB

// إعدادات الجلسة
define('SESSION_LIFETIME', 3600 * 24 * 7); // أسبوع واحد
define('CSRF_TOKEN_NAME', 'csrf_token');

// إعدادات التشفير
define('ENCRYPTION_KEY', 'your-secret-encryption-key-here');

// مستويات الصلاحيات
define('ROLE_ADMIN', 'admin');
define('ROLE_TEAM', 'team');
define('ROLE_CLIENT', 'client');

// حالات الرسائل
define('MESSAGE_UNREAD', 'unread');
define('MESSAGE_READ', 'read');
define('MESSAGE_IMPORTANT', 'important');
define('MESSAGE_FOLLOW_UP', 'follow_up');

// أنواع المنصات الاجتماعية
define('PLATFORM_FACEBOOK', 'facebook');
define('PLATFORM_INSTAGRAM', 'instagram');
define('PLATFORM_TIKTOK', 'tiktok');
define('PLATFORM_TWITTER', 'twitter');
define('PLATFORM_LINKEDIN', 'linkedin');
define('PLATFORM_SNAPCHAT', 'snapchat');

// إعدادات التقارير
define('REPORT_CACHE_TIME', 3600); // ساعة واحدة
define('PDF_TEMP_PATH', __DIR__ . '/../temp/');

// إعدادات الإشعارات
define('NOTIFICATION_CHECK_INTERVAL', 30); // 30 ثانية

// تفعيل/إلغاء تفعيل وضع التطوير
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
define('ERROR_LOG_PATH', __DIR__ . '/../logs/');

// إعدادات الأمان
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'mov', 'avi']);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات API Rate Limiting
define('API_RATE_LIMIT', 100); // طلب في الدقيقة
define('API_RATE_WINDOW', 60); // ثانية

// إعدادات الكاش
define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 1800); // 30 دقيقة
