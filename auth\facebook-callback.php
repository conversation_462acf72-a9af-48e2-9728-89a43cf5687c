<?php
/**
 * معالجة callback من فيسبوك
 * Social Media Management System
 */

require_once '../config/database.php';
require_once '../includes/database.php';
require_once '../includes/session.php';
require_once '../includes/functions.php';

$session = new SessionManager();
$db = Database::getInstance();

// التحقق من وجود كود التفويض
if (!isset($_GET['code'])) {
    $error = $_GET['error_description'] ?? 'فشل في تسجيل الدخول بفيسبوك';
    redirect('login.php?error=' . urlencode($error));
}

// التحقق من state للحماية من CSRF
if (!isset($_GET['state']) || $_GET['state'] !== ($_SESSION['facebook_state'] ?? '')) {
    redirect('login.php?error=' . urlencode('خطأ في التحقق من الأمان'));
}

$code = $_GET['code'];

try {
    // الحصول على access token
    $token_url = 'https://graph.facebook.com/v18.0/oauth/access_token';
    $token_params = [
        'client_id' => FB_APP_ID,
        'client_secret' => FB_APP_SECRET,
        'redirect_uri' => FB_REDIRECT_URI,
        'code' => $code
    ];
    
    $token_response = file_get_contents($token_url . '?' . http_build_query($token_params));
    $token_data = json_decode($token_response, true);
    
    if (!isset($token_data['access_token'])) {
        throw new Exception('فشل في الحصول على رمز الوصول من فيسبوك');
    }
    
    $access_token = $token_data['access_token'];
    
    // الحصول على بيانات المستخدم
    $user_url = 'https://graph.facebook.com/v18.0/me';
    $user_params = [
        'access_token' => $access_token,
        'fields' => 'id,name,email,picture.type(large)'
    ];
    
    $user_response = file_get_contents($user_url . '?' . http_build_query($user_params));
    $user_data = json_decode($user_response, true);
    
    if (!isset($user_data['id'])) {
        throw new Exception('فشل في الحصول على بيانات المستخدم من فيسبوك');
    }
    
    $facebook_id = $user_data['id'];
    $name = $user_data['name'];
    $email = $user_data['email'] ?? '';
    $profile_picture = $user_data['picture']['data']['url'] ?? '';
    
    // البحث عن المستخدم في قاعدة البيانات
    $existing_user = $db->selectOne(
        "SELECT * FROM users WHERE facebook_id = ? OR email = ?",
        [$facebook_id, $email]
    );
    
    if ($existing_user) {
        // تحديث بيانات المستخدم الموجود
        $db->update(
            "UPDATE users SET 
             name = ?, 
             email = ?, 
             profile_picture = ?, 
             facebook_id = ?,
             last_login = NOW() 
             WHERE id = ?",
            [$name, $email, $profile_picture, $facebook_id, $existing_user['id']]
        );
        
        $user_id = $existing_user['id'];
        $user_role = $existing_user['role'];
    } else {
        // إنشاء مستخدم جديد
        $user_id = $db->insert(
            "INSERT INTO users (facebook_id, name, email, profile_picture, role, status, created_at) 
             VALUES (?, ?, ?, ?, 'team', 'active', NOW())",
            [$facebook_id, $name, $email, $profile_picture]
        );
        
        if (!$user_id) {
            throw new Exception('فشل في إنشاء حساب المستخدم');
        }
        
        $user_role = 'team';
    }
    
    // تسجيل دخول المستخدم
    $login_data = [
        'id' => $user_id,
        'name' => $name,
        'email' => $email,
        'role' => $user_role,
        'facebook_id' => $facebook_id,
        'profile_picture' => $profile_picture
    ];
    
    $session->login($login_data);
    
    // حفظ access token للاستخدام لاحقاً
    $_SESSION['facebook_access_token'] = $access_token;
    
    // تسجيل النشاط
    log_activity($user_id, 'تسجيل دخول بفيسبوك', 'تسجيل دخول ناجح عبر فيسبوك');
    
    // جلب صفحات فيسبوك المرتبطة بالمستخدم
    $pages_url = 'https://graph.facebook.com/v18.0/me/accounts';
    $pages_params = [
        'access_token' => $access_token,
        'fields' => 'id,name,access_token,category,picture,fan_count'
    ];
    
    $pages_response = file_get_contents($pages_url . '?' . http_build_query($pages_params));
    $pages_data = json_decode($pages_response, true);
    
    if (isset($pages_data['data']) && !empty($pages_data['data'])) {
        // حفظ الصفحات في الجلسة لعرضها للمستخدم
        $_SESSION['facebook_pages'] = $pages_data['data'];
    }
    
    // إعادة التوجيه للصفحة الرئيسية أو صفحة ربط الحسابات
    if (isset($_SESSION['facebook_pages']) && !empty($_SESSION['facebook_pages'])) {
        redirect('../setup/link-accounts.php');
    } else {
        redirect('../index.php');
    }
    
} catch (Exception $e) {
    // تسجيل الخطأ
    if (LOG_ERRORS) {
        $error_message = date('Y-m-d H:i:s') . " - خطأ في تسجيل الدخول بفيسبوك: " . $e->getMessage() . "\n";
        file_put_contents(ERROR_LOG_PATH . 'facebook_auth.log', $error_message, FILE_APPEND);
    }
    
    redirect('login.php?error=' . urlencode('فشل في تسجيل الدخول بفيسبوك: ' . $e->getMessage()));
}
?>
