<?php
/**
 * إعداد قاعدة البيانات والنظام
 * Social Media Management System
 */

require_once '../config/database.php';

// التحقق من وجود قاعدة البيانات
try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>بدء إعداد النظام...</h2>";
    
    // قراءة ملف SQL
    $sql_file = __DIR__ . '/database_setup.sql';
    if (!file_exists($sql_file)) {
        die("ملف قاعدة البيانات غير موجود: " . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    
    // تقسيم الاستعلامات
    $queries = array_filter(array_map('trim', explode(';', $sql_content)));
    
    echo "<h3>إنشاء قاعدة البيانات والجداول...</h3>";
    echo "<ul>";
    
    foreach ($queries as $query) {
        if (empty($query) || strpos($query, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($query);
            
            // استخراج نوع الاستعلام للعرض
            $query_type = strtoupper(substr(trim($query), 0, 6));
            if ($query_type === 'CREATE') {
                if (strpos($query, 'DATABASE') !== false) {
                    echo "<li>✓ تم إنشاء قاعدة البيانات</li>";
                } else {
                    // استخراج اسم الجدول
                    preg_match('/CREATE TABLE\s+(\w+)/i', $query, $matches);
                    $table_name = $matches[1] ?? 'جدول غير معروف';
                    echo "<li>✓ تم إنشاء جدول: {$table_name}</li>";
                }
            } elseif ($query_type === 'INSERT') {
                echo "<li>✓ تم إدراج البيانات الأولية</li>";
            }
            
        } catch (PDOException $e) {
            echo "<li>✗ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</li>";
        }
    }
    
    echo "</ul>";
    
    // إنشاء المجلدات المطلوبة
    echo "<h3>إنشاء المجلدات المطلوبة...</h3>";
    echo "<ul>";
    
    $directories = [
        '../uploads',
        '../uploads/profiles',
        '../uploads/posts',
        '../uploads/reports',
        '../temp',
        '../logs'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<li>✓ تم إنشاء مجلد: " . basename($dir) . "</li>";
            } else {
                echo "<li>✗ فشل في إنشاء مجلد: " . basename($dir) . "</li>";
            }
        } else {
            echo "<li>✓ المجلد موجود بالفعل: " . basename($dir) . "</li>";
        }
    }
    
    echo "</ul>";
    
    // إنشاء ملف .htaccess للحماية
    echo "<h3>إعداد ملفات الحماية...</h3>";
    echo "<ul>";
    
    $htaccess_content = "# حماية ملفات الإعداد\n";
    $htaccess_content .= "<Files \"*.php\">\n";
    $htaccess_content .= "Order Allow,Deny\n";
    $htaccess_content .= "Deny from all\n";
    $htaccess_content .= "</Files>\n\n";
    $htaccess_content .= "# منع عرض محتويات المجلد\n";
    $htaccess_content .= "Options -Indexes\n";
    
    $protected_dirs = ['../config', '../includes', '../logs'];
    
    foreach ($protected_dirs as $dir) {
        $htaccess_file = $dir . '/.htaccess';
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            echo "<li>✓ تم إنشاء ملف الحماية في: " . basename($dir) . "</li>";
        } else {
            echo "<li>✗ فشل في إنشاء ملف الحماية في: " . basename($dir) . "</li>";
        }
    }
    
    echo "</ul>";
    
    // إنشاء مستخدمين افتراضيين
    echo "<h3>إعداد المستخدمين الافتراضيين...</h3>";
    
    // الاتصال بقاعدة البيانات الجديدة
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من وجود مستخدمين
    $users_count = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    
    if ($users_count == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $team_password = password_hash('team123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role, status) VALUES (?, ?, ?, ?, 'active')");
        
        // إنشاء المستخدم الإداري
        if ($stmt->execute(['مدير النظام', '<EMAIL>', $admin_password, 'admin'])) {
            echo "<p>✓ تم إنشاء المستخدم الإداري:</p>";
            echo "<ul>";
            echo "<li>البريد الإلكتروني: <EMAIL></li>";
            echo "<li>كلمة المرور: admin123</li>";
            echo "</ul>";
        }
        
        // إنشاء مستخدم فريق تجريبي
        if ($stmt->execute(['عضو الفريق', '<EMAIL>', $team_password, 'team'])) {
            echo "<p>✓ تم إنشاء مستخدم الفريق:</p>";
            echo "<ul>";
            echo "<li>البريد الإلكتروني: <EMAIL></li>";
            echo "<li>كلمة المرور: team123</li>";
            echo "</ul>";
        }
        
        echo "<p><strong>تأكد من تغيير كلمات المرور بعد تسجيل الدخول!</strong></p>";
    } else {
        echo "<p>✓ يوجد مستخدمون بالفعل في النظام</p>";
    }
    
    echo "<h2>تم إعداد النظام بنجاح! 🎉</h2>";
    echo "<p><a href='../auth/login.php' style='background: #3B82F6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب لصفحة تسجيل الدخول</a></p>";
    
    echo "<h3>بيانات تسجيل الدخول:</h3>";
    echo "<div style='background: #f0f9ff; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>المدير:</h4>";
    echo "<p>البريد: <EMAIL><br>كلمة المرور: admin123</p>";
    echo "<h4>عضو الفريق:</h4>";
    echo "<p>البريد: <EMAIL><br>كلمة المرور: team123</p>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h2>خطأ في الإعداد</h2>";
    echo "<p>خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خادم MySQL</li>";
    echo "<li>صحة بيانات الاتصال في ملف config/database.php</li>";
    echo "<li>وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - <?php echo APP_NAME; ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        h2, h3 {
            color: #333;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 10px;
        }
        ul {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        li {
            margin: 10px 0;
            padding: 5px 0;
        }
        .success { color: #059669; }
        .error { color: #DC2626; }
        .warning { color: #D97706; }
    </style>
</head>
<body>
