# نظام إدارة السوشيال ميديا المتكامل

نظام ويب متكامل لإدارة حسابات السوشيال ميديا الخاصة بالعملاء من مكان واحد، ليكون بديلاً عن Meta Business Suite وHootsuite.

## 🌟 المميزات الرئيسية

### 🔐 نظام التوثيق والصلاحيات
- تسجيل الدخول بحساب فيسبوك
- نظام صلاحيات متدرج (Admin, Team, Client)
- إدارة الجلسات الآمنة
- حماية من هجمات CSRF

### 📁 إدارة العملاء
- إنشاء ملفات للعملاء
- ربط الحسابات الاجتماعية (Facebook, Instagram, TikTok, Twitter, LinkedIn, Snapchat)
- تتبع بيانات موحدة لكل عميل

### 📥 صندوق الرسائل الموحد
- عرض جميع الرسائل من جميع المنصات
- تصنيف الرسائل (مقروء، غير مقروء، مهم، للمتابعة)
- الرد على الرسائل من واجهة واحدة
- إشعارات الرسائل الجديدة

### 💬 إدارة التعليقات
- عرض التعليقات من جميع المنصات
- الرد على التعليقات
- فلترة التعليقات حسب الصفحة والمنشور
- إدارة التعليقات المعلقة

### 📝 النشر والجدولة
- نشر المحتوى على منصات متعددة
- دعم النص والصور والفيديوهات
- جدولة المنشورات
- معاينة المحتوى قبل النشر

### 📊 التقارير التفصيلية
- تقارير الأداء لكل عميل
- إحصائيات التفاعل والوصول
- تقارير الإعلانات
- تصدير التقارير إلى PDF
- تخصيص التقارير بالشعار والملاحظات

### 🔔 نظام الإشعارات
- إشعارات الرسائل الجديدة
- تنبيهات التعليقات
- إشعارات النظام
- تحديث في الوقت الفعلي

## 🛠️ التقنيات المستخدمة

- **الواجهة الأمامية**: HTML5, Tailwind CSS, JavaScript
- **الواجهة الخلفية**: PHP (بدون إطار عمل)
- **قاعدة البيانات**: MySQL
- **التكامل**: Facebook Graph API, Instagram API, Twitter API, LinkedIn API
- **التوثيق**: OAuth 2.0
- **التقارير**: PDF Generation
- **الأمان**: CSRF Protection, Session Management, Rate Limiting

## 📋 متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx مع mod_rewrite
- OpenSSL extension
- cURL extension
- GD extension (للصور)
- JSON extension

## 🚀 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd social-media-manager
```

### 2. إعداد قاعدة البيانات
1. قم بإنشاء قاعدة بيانات MySQL جديدة
2. عدّل ملف `config/database.php` بإعدادات قاعدة البيانات الخاصة بك
3. قم بزيارة `install/setup.php` لإعداد قاعدة البيانات

### 3. إعداد APIs
1. **Facebook API**:
   - إنشاء تطبيق على Facebook Developers
   - الحصول على App ID و App Secret
   - تحديث الإعدادات في `config/database.php`

2. **Instagram API**:
   - ربط Instagram بتطبيق Facebook
   - الحصول على Client ID و Client Secret

3. **Twitter API**:
   - إنشاء تطبيق على Twitter Developer Portal
   - الحصول على API Keys

4. **LinkedIn API**:
   - إنشاء تطبيق على LinkedIn Developer Portal
   - الحصول على Client ID و Client Secret

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 755 temp/
chmod 755 logs/
```

### 5. تسجيل الدخول الأول
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`
- **تأكد من تغيير كلمة المرور فوراً!**

## 📁 هيكل المشروع

```
social-media-manager/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات والتطبيق
├── includes/
│   ├── database.php          # كلاس إدارة قاعدة البيانات
│   ├── session.php           # إدارة الجلسات والأمان
│   └── functions.php         # الوظائف المساعدة
├── auth/
│   ├── login.php            # صفحة تسجيل الدخول
│   ├── logout.php           # تسجيل الخروج
│   └── facebook-login.php   # تسجيل الدخول بفيسبوك
├── clients/                 # إدارة العملاء
├── messages/               # صندوق الرسائل
├── comments/               # إدارة التعليقات
├── posts/                  # النشر والجدولة
├── reports/                # التقارير
├── admin/                  # لوحة الإدارة
├── uploads/                # ملفات المستخدمين
├── temp/                   # ملفات مؤقتة
├── logs/                   # ملفات السجلات
├── install/
│   ├── setup.php           # إعداد النظام
│   └── database_setup.sql  # هيكل قاعدة البيانات
└── index.php               # الصفحة الرئيسية
```

## 🔒 الأمان

- حماية من هجمات SQL Injection
- حماية من هجمات XSS
- حماية من هجمات CSRF
- تشفير كلمات المرور
- إدارة الجلسات الآمنة
- Rate Limiting للـ APIs
- تسجيل الأنشطة

## 📝 الاستخدام

### إضافة عميل جديد
1. اذهب إلى قسم "العملاء"
2. انقر على "إضافة عميل جديد"
3. أدخل بيانات العميل
4. اربط الحسابات الاجتماعية

### إدارة الرسائل
1. اذهب إلى قسم "الرسائل"
2. اعرض الرسائل من جميع المنصات
3. اقرأ الرسائل ورد عليها
4. صنف الرسائل حسب الأولوية

### إنشاء تقرير
1. اذهب إلى قسم "التقارير"
2. اختر العميل والفترة الزمنية
3. حدد المنصات المطلوبة
4. اضغط "إنشاء تقرير"
5. حمل التقرير بصيغة PDF

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🔄 التحديثات المستقبلية

- [ ] دعم TikTok API
- [ ] دعم Snapchat API
- [ ] تطبيق الهاتف المحمول
- [ ] ذكاء اصطناعي لتحليل المشاعر
- [ ] جدولة متقدمة للمحتوى
- [ ] تقارير تفاعلية
- [ ] دعم المزيد من اللغات

---

**تم تطوير هذا النظام بـ ❤️ لتسهيل إدارة السوشيال ميديا**
